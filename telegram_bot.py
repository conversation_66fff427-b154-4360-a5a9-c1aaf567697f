#!/usr/bin/env python3
"""
Bot Telegram para Controle do Sistema de Trading Crypto IDX
Integra todas as funcionalidades do crypto_idx_bot.py com interface Telegram
"""

import telebot
from telebot import types
import threading
import time
import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
import logging
from pathlib import Path

# Importar funções do bot principal
from crypto_idx_bot import (
    obter_dados_realtime, gerar_sinais, executar_analise_unica,
    verificar_resultados, gerar_analise_estrategia, exibir_analise_estrategia,
    get_current_time_sp, TIMEZONE_SP, BINOMO_CONFIG
)

# Carregar variáveis de ambiente
load_dotenv()

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CryptoTradingBot:
    def __init__(self):
        self.token = os.getenv('TELEGRAM_TOKEN')
        self.chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        if not self.token or not self.chat_id:
            raise ValueError("TELEGRAM_TOKEN e TELEGRAM_CHAT_ID devem estar configurados no .env")
        
        self.bot = telebot.TeleBot(self.token)
        self.setup_handlers()
        
        # Estado do bot
        self.is_running_realtime = False
        self.is_running_backtest = False
        self.realtime_thread = None
        self.backtest_thread = None
        
        # Configurações
        self.config = {
            'symbol': os.getenv('BINOMO_SYMBOL', 'Z-CRY%2FIDX'),
            'timeframe': os.getenv('BINOMO_TIMEFRAME', '1'),
            'min_confidence': float(os.getenv('MIN_CONFIDENCE', '55.0')),
            'auto_send_signals': True,
            'send_analysis': True
        }
        
        logger.info("🚀 Bot Telegram inicializado com sucesso!")

    def create_main_keyboard(self):
        """Cria o teclado principal com botões"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        # Linha 1: Sinais e Análise
        btn_signal = types.InlineKeyboardButton("🎯 Gerar Sinal", callback_data="generate_signal")
        btn_analysis = types.InlineKeyboardButton("📊 Análise Única", callback_data="single_analysis")
        keyboard.row(btn_signal, btn_analysis)
        
        # Linha 2: Tempo Real e Backtest
        btn_realtime = types.InlineKeyboardButton("🔴 Iniciar Tempo Real", callback_data="start_realtime")
        btn_backtest = types.InlineKeyboardButton("📈 Backtest", callback_data="start_backtest")
        keyboard.row(btn_realtime, btn_backtest)
        
        # Linha 3: Resultados e Estatísticas
        btn_results = types.InlineKeyboardButton("✅ Verificar Resultados", callback_data="check_results")
        btn_stats = types.InlineKeyboardButton("📊 Estatísticas", callback_data="show_stats")
        keyboard.row(btn_results, btn_stats)
        
        # Linha 4: Configurações e Status
        btn_config = types.InlineKeyboardButton("⚙️ Configurações", callback_data="show_config")
        btn_status = types.InlineKeyboardButton("📡 Status", callback_data="show_status")
        keyboard.row(btn_config, btn_status)
        
        # Linha 5: Parar operações
        btn_stop = types.InlineKeyboardButton("🛑 Parar Tudo", callback_data="stop_all")
        keyboard.row(btn_stop)
        
        return keyboard

    def create_config_keyboard(self):
        """Cria teclado de configurações"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        # Toggle auto send signals
        auto_text = "🔔 Auto Sinais: ON" if self.config['auto_send_signals'] else "🔕 Auto Sinais: OFF"
        btn_auto = types.InlineKeyboardButton(auto_text, callback_data="toggle_auto_signals")
        
        # Toggle analysis
        analysis_text = "📊 Auto Análise: ON" if self.config['send_analysis'] else "📊 Auto Análise: OFF"
        btn_analysis = types.InlineKeyboardButton(analysis_text, callback_data="toggle_analysis")
        
        keyboard.row(btn_auto)
        keyboard.row(btn_analysis)
        
        # Configurar timeframe
        btn_timeframe = types.InlineKeyboardButton("⏱️ Timeframe", callback_data="config_timeframe")
        btn_confidence = types.InlineKeyboardButton("🎯 Confiança Min", callback_data="config_confidence")
        keyboard.row(btn_timeframe, btn_confidence)
        
        # Voltar
        btn_back = types.InlineKeyboardButton("🔙 Voltar", callback_data="main_menu")
        keyboard.row(btn_back)
        
        return keyboard

    def create_realtime_keyboard(self):
        """Cria teclado para controle em tempo real"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        if self.is_running_realtime:
            btn_stop = types.InlineKeyboardButton("🛑 Parar Tempo Real", callback_data="stop_realtime")
            btn_status = types.InlineKeyboardButton("📊 Status Atual", callback_data="realtime_status")
            keyboard.row(btn_stop, btn_status)
        else:
            btn_start = types.InlineKeyboardButton("▶️ Iniciar Tempo Real", callback_data="start_realtime")
            keyboard.row(btn_start)
        
        btn_back = types.InlineKeyboardButton("🔙 Menu Principal", callback_data="main_menu")
        keyboard.row(btn_back)
        
        return keyboard

    def setup_handlers(self):
        """Configura os handlers do bot"""
        
        @self.bot.message_handler(commands=['start'])
        def start_command(message):
            welcome_text = (
                "🚀 **BOT CRYPTO IDX - BINOMO**\n\n"
                "🎯 **Funcionalidades Disponíveis:**\n"
                "• Geração de sinais em tempo real\n"
                "• Análise técnica avançada\n"
                "• Backtest de estratégias\n"
                "• Tracking de resultados\n"
                "• Estatísticas detalhadas\n\n"
                "📊 **Estratégia Otimizada v2.0:**\n"
                "• Multi-indicador avançado\n"
                "• Sistema de confiança adaptativo\n"
                "• Fuso horário São Paulo\n"
                "• API Binomo em tempo real\n\n"
                "Escolha uma opção abaixo:"
            )
            
            self.bot.send_message(
                message.chat.id, 
                welcome_text, 
                reply_markup=self.create_main_keyboard(),
                parse_mode='Markdown'
            )

        @self.bot.callback_query_handler(func=lambda call: True)
        def callback_handler(call):
            try:
                self.handle_callback(call)
            except Exception as e:
                logger.error(f"Erro no callback: {e}")
                self.bot.answer_callback_query(call.id, "❌ Erro interno")

    def handle_callback(self, call):
        """Manipula callbacks dos botões"""
        data = call.data
        chat_id = call.message.chat.id
        message_id = call.message.message_id
        
        if data == "main_menu":
            self.show_main_menu(chat_id, message_id)
            
        elif data == "generate_signal":
            self.generate_single_signal(chat_id, message_id)
            
        elif data == "single_analysis":
            self.run_single_analysis(chat_id, message_id)
            
        elif data == "start_realtime":
            self.start_realtime_trading(chat_id, message_id)
            
        elif data == "stop_realtime":
            self.stop_realtime_trading(chat_id, message_id)
            
        elif data == "start_backtest":
            self.start_backtest(chat_id, message_id)
            
        elif data == "check_results":
            self.check_pending_results(chat_id, message_id)
            
        elif data == "show_stats":
            self.show_statistics(chat_id, message_id)
            
        elif data == "show_config":
            self.show_configuration(chat_id, message_id)
            
        elif data == "show_status":
            self.show_bot_status(chat_id, message_id)
            
        elif data == "stop_all":
            self.stop_all_operations(chat_id, message_id)
            
        elif data == "toggle_auto_signals":
            self.toggle_auto_signals(chat_id, message_id)
            
        elif data == "toggle_analysis":
            self.toggle_analysis(chat_id, message_id)
            
        elif data == "realtime_status":
            self.show_realtime_status(chat_id, message_id)
        
        self.bot.answer_callback_query(call.id)

    def show_main_menu(self, chat_id, message_id=None):
        """Mostra o menu principal"""
        text = (
            "🎯 **CRYPTO IDX BOT - MENU PRINCIPAL**\n\n"
            f"🕐 Horário: {get_current_time_sp().strftime('%H:%M:%S')} (SP)\n"
            f"📊 Símbolo: {self.config['symbol']}\n"
            f"⏱️ Timeframe: {self.config['timeframe']}m\n"
            f"🎯 Confiança Mín: {self.config['min_confidence']}%\n\n"
            "Escolha uma opção:"
        )
        
        if message_id:
            self.bot.edit_message_text(
                text, chat_id, message_id,
                reply_markup=self.create_main_keyboard(),
                parse_mode='Markdown'
            )
        else:
            self.bot.send_message(
                chat_id, text,
                reply_markup=self.create_main_keyboard(),
                parse_mode='Markdown'
            )

    def generate_single_signal(self, chat_id, message_id):
        """Gera um sinal único"""
        self.bot.edit_message_text(
            "🔄 Gerando sinal... Aguarde...",
            chat_id, message_id
        )
        
        try:
            # Obter dados
            df = obter_dados_realtime(count=100, timeframe=self.config['timeframe'])
            
            if df is None or len(df) == 0:
                self.bot.edit_message_text(
                    "❌ Erro ao obter dados da API\n\n🔙 Voltando ao menu...",
                    chat_id, message_id,
                    reply_markup=self.create_main_keyboard()
                )
                return
            
            # Gerar sinais
            hora_ultima_vela = df.iloc[-1]['timestamp']
            sinais = gerar_sinais(df, hora_ultima_vela)
            
            if sinais:
                sinal = sinais[0]  # Pegar primeiro sinal
                
                # Formatear mensagem do sinal
                confidence_emoji = {
                    "MUITO_ALTA": "🔥",
                    "ALTA": "⭐",
                    "MEDIA": "📊",
                    "BAIXA": "⚠️"
                }
                
                confidence_level = sinal.get('confidence_level', 'MEDIA')
                score = sinal.get('score', 0)
                
                signal_text = (
                    f"🎯 **SINAL GERADO**\n\n"
                    f"📈 **Direção:** {sinal['direcao']}\n"
                    f"⏰ **Horário:** {sinal['hora']} (UTC) | {get_current_time_sp().strftime('%H:%M')} (SP)\n"
                    f"🎯 **Confiança:** {confidence_emoji.get(confidence_level, '📊')} {confidence_level}\n"
                    f"📊 **Score:** {score:.1f}/100\n\n"
                    f"✅ **Confirmações ({len(sinal['confirmacoes'])}):**\n"
                )
                
                for i, conf in enumerate(sinal['confirmacoes'][:5], 1):  # Limitar a 5
                    signal_text += f"{i}. {conf}\n"
                
                if len(sinal['confirmacoes']) > 5:
                    signal_text += f"... e mais {len(sinal['confirmacoes']) - 5} confirmações\n"
                
                signal_text += f"\n💰 **Preço:** {df.iloc[-1]['close']:.5f}"
                
                self.bot.edit_message_text(
                    signal_text,
                    chat_id, message_id,
                    reply_markup=self.create_main_keyboard(),
                    parse_mode='Markdown'
                )
                
            else:
                self.bot.edit_message_text(
                    "⚪ Nenhum sinal encontrado no momento\n\n"
                    "💡 Tente novamente em alguns minutos",
                    chat_id, message_id,
                    reply_markup=self.create_main_keyboard()
                )
                
        except Exception as e:
            logger.error(f"Erro ao gerar sinal: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao gerar sinal: {str(e)[:100]}...",
                chat_id, message_id,
                reply_markup=self.create_main_keyboard()
            )

    def run_single_analysis(self, chat_id, message_id):
        """Executa análise única"""
        self.bot.edit_message_text(
            "📊 Executando análise completa... Aguarde...",
            chat_id, message_id
        )

        try:
            # Executar análise
            sinais = executar_analise_unica(salvar_sinais=True)

            if sinais:
                analysis_text = (
                    f"📊 **ANÁLISE COMPLETA**\n\n"
                    f"🎯 **Sinais Encontrados:** {len(sinais)}\n"
                    f"🕐 **Horário:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)\n\n"
                )

                for i, sinal in enumerate(sinais[:3], 1):  # Mostrar até 3 sinais
                    confidence_level = sinal.get('confidence_level', 'MEDIA')
                    score = sinal.get('score', 0)

                    analysis_text += (
                        f"**Sinal {i}:**\n"
                        f"📈 {sinal['direcao']} às {sinal['hora']}\n"
                        f"🎯 {confidence_level} ({score:.1f}/100)\n"
                        f"✅ {len(sinal['confirmacoes'])} confirmações\n\n"
                    )

                if len(sinais) > 3:
                    analysis_text += f"... e mais {len(sinais) - 3} sinais\n\n"

                analysis_text += "💾 Todos os sinais foram salvos para tracking!"

            else:
                analysis_text = (
                    "📊 **ANÁLISE COMPLETA**\n\n"
                    "⚪ Nenhum sinal encontrado no momento\n\n"
                    "💡 Condições de mercado não atendem aos critérios da estratégia"
                )

            self.bot.edit_message_text(
                analysis_text,
                chat_id, message_id,
                reply_markup=self.create_main_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro na análise: {e}")
            self.bot.edit_message_text(
                f"❌ Erro na análise: {str(e)[:100]}...",
                chat_id, message_id,
                reply_markup=self.create_main_keyboard()
            )

    def start_realtime_trading(self, chat_id, message_id):
        """Inicia trading em tempo real"""
        if self.is_running_realtime:
            self.bot.edit_message_text(
                "⚠️ Trading em tempo real já está ativo!",
                chat_id, message_id,
                reply_markup=self.create_realtime_keyboard()
            )
            return

        self.bot.edit_message_text(
            "🔴 Iniciando trading em tempo real...",
            chat_id, message_id
        )

        self.is_running_realtime = True
        self.realtime_thread = threading.Thread(target=self.realtime_worker)
        self.realtime_thread.daemon = True
        self.realtime_thread.start()

        self.bot.edit_message_text(
            "🔴 **TRADING EM TEMPO REAL ATIVO**\n\n"
            "📊 O bot está analisando o mercado continuamente\n"
            "🎯 Sinais serão enviados automaticamente\n"
            "⏱️ Verificação a cada 60 segundos\n\n"
            "Use os botões abaixo para controlar:",
            chat_id, message_id,
            reply_markup=self.create_realtime_keyboard(),
            parse_mode='Markdown'
        )

    def stop_realtime_trading(self, chat_id, message_id):
        """Para trading em tempo real"""
        self.is_running_realtime = False

        self.bot.edit_message_text(
            "🛑 **TRADING EM TEMPO REAL PARADO**\n\n"
            "📊 Análise contínua interrompida\n"
            "💾 Todos os dados foram salvos\n\n"
            "Use o botão abaixo para reiniciar:",
            chat_id, message_id,
            reply_markup=self.create_realtime_keyboard(),
            parse_mode='Markdown'
        )

    def realtime_worker(self):
        """Worker para trading em tempo real"""
        logger.info("🔴 Iniciando worker de tempo real")
        ciclo = 0

        while self.is_running_realtime:
            try:
                ciclo += 1
                logger.info(f"🔄 Ciclo #{ciclo} - Tempo real")

                # Obter dados
                df = obter_dados_realtime(count=100, timeframe=self.config['timeframe'])

                if df is None or len(df) == 0:
                    logger.warning("⚠️ Falha ao obter dados - tentando novamente em 30s")
                    time.sleep(30)
                    continue

                # Gerar sinais
                hora_ultima_vela = df.iloc[-1]['timestamp']
                preco_atual = df.iloc[-1]['close']
                sinais = gerar_sinais(df, hora_ultima_vela)

                # Enviar sinais se configurado
                if sinais and self.config['auto_send_signals']:
                    for sinal in sinais:
                        self.send_realtime_signal(sinal, preco_atual)

                # Verificar resultados a cada 5 ciclos
                if ciclo % 5 == 0:
                    verificar_resultados()

                    if self.config['send_analysis']:
                        self.send_periodic_analysis()

                # Aguardar próximo ciclo
                time.sleep(60)

            except Exception as e:
                logger.error(f"❌ Erro no worker tempo real: {e}")
                time.sleep(30)

        logger.info("🛑 Worker de tempo real finalizado")

    def send_realtime_signal(self, sinal, preco_atual):
        """Envia sinal em tempo real"""
        try:
            confidence_emoji = {
                "MUITO_ALTA": "🔥",
                "ALTA": "⭐",
                "MEDIA": "📊",
                "BAIXA": "⚠️"
            }

            confidence_level = sinal.get('confidence_level', 'MEDIA')
            score = sinal.get('score', 0)

            # Filtrar por confiança mínima
            if score < self.config['min_confidence']:
                logger.info(f"Sinal filtrado - Score {score:.1f} < {self.config['min_confidence']}")
                return

            signal_text = (
                f"🚨 **SINAL EM TEMPO REAL**\n\n"
                f"📈 **{sinal['direcao']}** 📈\n"
                f"⏰ {sinal['hora']} (UTC) | {get_current_time_sp().strftime('%H:%M')} (SP)\n"
                f"💰 **Preço:** {preco_atual:.5f}\n"
                f"🎯 **Confiança:** {confidence_emoji.get(confidence_level, '📊')} {confidence_level}\n"
                f"📊 **Score:** {score:.1f}/100\n\n"
                f"✅ **Top Confirmações:**\n"
            )

            # Mostrar top 4 confirmações
            for i, conf in enumerate(sinal['confirmacoes'][:4], 1):
                signal_text += f"{i}. {conf[:50]}{'...' if len(conf) > 50 else ''}\n"

            signal_text += f"\n⏱️ **Expiração:** 1 minuto\n🎯 **Símbolo:** {self.config['symbol']}"

            self.bot.send_message(
                self.chat_id,
                signal_text,
                parse_mode='Markdown'
            )

            logger.info(f"📤 Sinal enviado: {sinal['direcao']} - Score: {score:.1f}")

        except Exception as e:
            logger.error(f"❌ Erro ao enviar sinal: {e}")

    def send_periodic_analysis(self):
        """Envia análise periódica"""
        try:
            # Gerar análise
            analise = gerar_analise_estrategia()

            if analise:
                stats = analise['estatisticas_gerais']

                analysis_text = (
                    f"📊 **RELATÓRIO PERIÓDICO**\n\n"
                    f"🎯 **Total:** {stats['total_sinais']} sinais\n"
                    f"✅ **Gains:** {stats['gains']} ({stats['win_rate']}%)\n"
                    f"❌ **Losses:** {stats['losses']}\n"
                    f"💰 **Lucro:** {stats['lucro_total']:+.2f}\n\n"
                    f"🕐 **Atualizado:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)"
                )

                self.bot.send_message(
                    self.chat_id,
                    analysis_text,
                    parse_mode='Markdown'
                )

        except Exception as e:
            logger.error(f"❌ Erro ao enviar análise periódica: {e}")

    def start_backtest(self, chat_id, message_id):
        """Inicia backtest"""
        self.bot.edit_message_text(
            "📈 **BACKTEST**\n\n"
            "🔄 Executando backtest da estratégia...\n"
            "⏳ Isso pode levar alguns minutos...",
            chat_id, message_id,
            parse_mode='Markdown'
        )

        try:
            # Executar backtest (simulação com dados históricos)
            df = obter_dados_realtime(count=500, timeframe=self.config['timeframe'])

            if df is None or len(df) == 0:
                self.bot.edit_message_text(
                    "❌ Erro ao obter dados para backtest",
                    chat_id, message_id,
                    reply_markup=self.create_main_keyboard()
                )
                return

            # Simular sinais em dados históricos
            total_signals = 0
            wins = 0
            losses = 0

            for i in range(50, len(df) - 1):  # Deixar espaço para indicadores
                df_slice = df.iloc[:i+1]
                hora_vela = df_slice.iloc[-1]['timestamp']
                sinais = gerar_sinais(df_slice, hora_vela)

                if sinais:
                    total_signals += 1
                    sinal = sinais[0]

                    # Simular resultado (próxima vela)
                    preco_entrada = df.iloc[i]['close']
                    preco_saida = df.iloc[i+1]['close']

                    if sinal['direcao'] == 'CALL':
                        resultado = preco_saida > preco_entrada
                    else:
                        resultado = preco_saida < preco_entrada

                    if resultado:
                        wins += 1
                    else:
                        losses += 1

            # Calcular estatísticas
            win_rate = (wins / total_signals * 100) if total_signals > 0 else 0
            profit = (wins * 0.85) - losses  # 85% payout

            backtest_text = (
                f"📈 **RESULTADO DO BACKTEST**\n\n"
                f"📊 **Período:** {len(df)} velas ({self.config['timeframe']}m)\n"
                f"🎯 **Total de Sinais:** {total_signals}\n"
                f"✅ **Wins:** {wins}\n"
                f"❌ **Losses:** {losses}\n"
                f"📊 **Win Rate:** {win_rate:.2f}%\n"
                f"💰 **Lucro Simulado:** {profit:+.2f}\n\n"
                f"⚠️ *Resultados passados não garantem performance futura*"
            )

            self.bot.edit_message_text(
                backtest_text,
                chat_id, message_id,
                reply_markup=self.create_main_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro no backtest: {e}")
            self.bot.edit_message_text(
                f"❌ Erro no backtest: {str(e)[:100]}...",
                chat_id, message_id,
                reply_markup=self.create_main_keyboard()
            )

    def check_pending_results(self, chat_id, message_id):
        """Verifica resultados pendentes"""
        self.bot.edit_message_text(
            "🔍 Verificando resultados pendentes...",
            chat_id, message_id
        )

        try:
            verificar_resultados()

            self.bot.edit_message_text(
                "✅ **RESULTADOS VERIFICADOS**\n\n"
                "🔄 Todos os sinais pendentes foram atualizados\n"
                "📊 Use 'Estatísticas' para ver os resultados\n\n"
                "💾 Dados salvos automaticamente",
                chat_id, message_id,
                reply_markup=self.create_main_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro ao verificar resultados: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao verificar resultados: {str(e)[:100]}...",
                chat_id, message_id,
                reply_markup=self.create_main_keyboard()
            )

    def show_statistics(self, chat_id, message_id):
        """Mostra estatísticas detalhadas"""
        self.bot.edit_message_text(
            "📊 Carregando estatísticas...",
            chat_id, message_id
        )

        try:
            analise = gerar_analise_estrategia()

            if not analise:
                self.bot.edit_message_text(
                    "❌ Nenhuma estatística disponível\n\n"
                    "💡 Execute algumas análises primeiro",
                    chat_id, message_id,
                    reply_markup=self.create_main_keyboard()
                )
                return

            stats = analise['estatisticas_gerais']
            direcoes = analise['analise_por_direcao']

            stats_text = (
                f"📊 **ESTATÍSTICAS COMPLETAS**\n\n"
                f"🎯 **Geral:**\n"
                f"• Total: {stats['total_sinais']} sinais\n"
                f"• Wins: {stats['gains']} ({stats['win_rate']}%)\n"
                f"• Losses: {stats['losses']}\n"
                f"• Lucro: {stats['lucro_total']:+.2f}\n"
                f"• Média: {stats['lucro_medio']:+.4f}\n\n"
                f"📈 **Por Direção:**\n"
            )

            for direcao, dados in direcoes.items():
                if dados['total'] > 0:
                    stats_text += f"• {direcao}: {dados['wins']}/{dados['total']} ({dados['win_rate']}%)\n"

            # Top confirmações
            if 'tipos_confirmacao_mais_eficazes' in analise:
                stats_text += f"\n🏆 **Top Confirmações:**\n"
                for i, (conf, dados) in enumerate(list(analise['tipos_confirmacao_mais_eficazes'].items())[:3], 1):
                    conf_name = conf.replace('_', ' ').title()[:25]
                    stats_text += f"{i}. {conf_name}: {dados['win_rate']}%\n"

            stats_text += f"\n🕐 **Atualizado:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)"

            self.bot.edit_message_text(
                stats_text,
                chat_id, message_id,
                reply_markup=self.create_main_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro ao mostrar estatísticas: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao carregar estatísticas: {str(e)[:100]}...",
                chat_id, message_id,
                reply_markup=self.create_main_keyboard()
            )

    def show_configuration(self, chat_id, message_id):
        """Mostra configurações"""
        config_text = (
            f"⚙️ **CONFIGURAÇÕES**\n\n"
            f"📊 **Símbolo:** {self.config['symbol']}\n"
            f"⏱️ **Timeframe:** {self.config['timeframe']}m\n"
            f"🎯 **Confiança Mín:** {self.config['min_confidence']}%\n\n"
            f"🔔 **Auto Sinais:** {'✅ ON' if self.config['auto_send_signals'] else '❌ OFF'}\n"
            f"📊 **Auto Análise:** {'✅ ON' if self.config['send_analysis'] else '❌ OFF'}\n\n"
            f"💡 Use os botões para alterar:"
        )

        self.bot.edit_message_text(
            config_text,
            chat_id, message_id,
            reply_markup=self.create_config_keyboard(),
            parse_mode='Markdown'
        )

    def show_bot_status(self, chat_id, message_id):
        """Mostra status do bot"""
        try:
            # Testar conexão com API
            df = obter_dados_realtime(count=1, timeframe="1")
            api_status = "✅ Online" if df is not None else "❌ Offline"

            status_text = (
                f"📡 **STATUS DO BOT**\n\n"
                f"🤖 **Bot:** ✅ Online\n"
                f"📊 **API Binomo:** {api_status}\n"
                f"🔴 **Tempo Real:** {'✅ Ativo' if self.is_running_realtime else '❌ Inativo'}\n"
                f"📈 **Backtest:** {'🔄 Executando' if self.is_running_backtest else '⚪ Parado'}\n\n"
                f"🕐 **Horário:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)\n"
                f"📱 **Chat ID:** {self.chat_id}\n"
                f"🎯 **Símbolo:** {self.config['symbol']}\n\n"
                f"💾 **Arquivos:**\n"
                f"• Sinais: {'✅' if Path('results/signals_history.json').exists() else '❌'}\n"
                f"• Análise: {'✅' if Path('results/strategy_analysis.json').exists() else '❌'}"
            )

            self.bot.edit_message_text(
                status_text,
                chat_id, message_id,
                reply_markup=self.create_main_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro ao mostrar status: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao carregar status: {str(e)[:100]}...",
                chat_id, message_id,
                reply_markup=self.create_main_keyboard()
            )

    def stop_all_operations(self, chat_id, message_id):
        """Para todas as operações"""
        self.is_running_realtime = False
        self.is_running_backtest = False

        self.bot.edit_message_text(
            "🛑 **TODAS AS OPERAÇÕES PARADAS**\n\n"
            "✅ Trading em tempo real: Parado\n"
            "✅ Backtest: Parado\n"
            "✅ Workers: Finalizados\n\n"
            "💾 Todos os dados foram salvos\n"
            "🔄 Use o menu para reiniciar operações",
            chat_id, message_id,
            reply_markup=self.create_main_keyboard(),
            parse_mode='Markdown'
        )

    def toggle_auto_signals(self, chat_id, message_id):
        """Alterna envio automático de sinais"""
        self.config['auto_send_signals'] = not self.config['auto_send_signals']
        status = "ativado" if self.config['auto_send_signals'] else "desativado"

        self.bot.edit_message_text(
            f"🔔 **Auto Sinais {status.upper()}**\n\n"
            f"{'✅' if self.config['auto_send_signals'] else '❌'} Sinais serão enviados automaticamente: {status}\n\n"
            "⚙️ Configurações atualizadas:",
            chat_id, message_id,
            reply_markup=self.create_config_keyboard(),
            parse_mode='Markdown'
        )

    def toggle_analysis(self, chat_id, message_id):
        """Alterna envio automático de análises"""
        self.config['send_analysis'] = not self.config['send_analysis']
        status = "ativado" if self.config['send_analysis'] else "desativado"

        self.bot.edit_message_text(
            f"📊 **Auto Análise {status.upper()}**\n\n"
            f"{'✅' if self.config['send_analysis'] else '❌'} Análises periódicas: {status}\n\n"
            "⚙️ Configurações atualizadas:",
            chat_id, message_id,
            reply_markup=self.create_config_keyboard(),
            parse_mode='Markdown'
        )

    def show_realtime_status(self, chat_id, message_id):
        """Mostra status do tempo real"""
        if not self.is_running_realtime:
            self.bot.edit_message_text(
                "❌ Trading em tempo real não está ativo",
                chat_id, message_id,
                reply_markup=self.create_realtime_keyboard()
            )
            return

        try:
            # Obter dados atuais
            df = obter_dados_realtime(count=5, timeframe=self.config['timeframe'])

            if df is not None and len(df) > 0:
                preco_atual = df.iloc[-1]['close']
                variacao = ((df.iloc[-1]['close'] - df.iloc[-2]['close']) / df.iloc[-2]['close']) * 100

                status_text = (
                    f"🔴 **TEMPO REAL ATIVO**\n\n"
                    f"💰 **Preço:** {preco_atual:.5f}\n"
                    f"📈 **Variação:** {variacao:+.2f}%\n"
                    f"🕐 **Última Atualização:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)\n\n"
                    f"⚙️ **Configurações:**\n"
                    f"• Timeframe: {self.config['timeframe']}m\n"
                    f"• Confiança Mín: {self.config['min_confidence']}%\n"
                    f"• Auto Sinais: {'✅' if self.config['auto_send_signals'] else '❌'}\n"
                    f"• Auto Análise: {'✅' if self.config['send_analysis'] else '❌'}"
                )
            else:
                status_text = (
                    f"🔴 **TEMPO REAL ATIVO**\n\n"
                    f"⚠️ Erro ao obter dados atuais\n"
                    f"🔄 Sistema tentando reconectar...\n\n"
                    f"🕐 **Status:** {get_current_time_sp().strftime('%H:%M:%S')} (SP)"
                )

            self.bot.edit_message_text(
                status_text,
                chat_id, message_id,
                reply_markup=self.create_realtime_keyboard(),
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Erro ao mostrar status tempo real: {e}")
            self.bot.edit_message_text(
                f"❌ Erro ao carregar status: {str(e)[:100]}...",
                chat_id, message_id,
                reply_markup=self.create_realtime_keyboard()
            )

    def run(self):
        """Executa o bot"""
        logger.info("🚀 Iniciando Bot Telegram...")
        logger.info(f"📱 Chat ID configurado: {self.chat_id}")
        
        # Enviar mensagem de inicialização
        try:
            startup_msg = (
                "🚀 **BOT CRYPTO IDX ATIVO!**\n\n"
                f"🕐 Iniciado em: {get_current_time_sp().strftime('%d/%m/%Y %H:%M:%S')} (SP)\n"
                f"📊 Símbolo: {self.config['symbol']}\n"
                f"⏱️ Timeframe: {self.config['timeframe']}m\n\n"
                "Use /start para acessar o menu principal!"
            )
            
            self.bot.send_message(self.chat_id, startup_msg, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Erro ao enviar mensagem de inicialização: {e}")
        
        # Iniciar polling
        logger.info("📡 Bot em execução... Pressione Ctrl+C para parar")
        self.bot.polling(none_stop=True)

if __name__ == "__main__":
    try:
        bot = CryptoTradingBot()
        bot.run()
    except KeyboardInterrupt:
        print("\n🛑 Bot interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro fatal: {e}")
