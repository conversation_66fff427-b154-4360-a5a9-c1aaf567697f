from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import pytesseract
import cv2
import pandas as pd
import numpy as np
import time
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

# Configurar pytesseract
pytesseract.pytesseract.tesseract_cmd = os.getenv('TESSERACT_CMD', r'C:\Program Files\Tesseract-OCR\tesseract.exe')

# ============================
# INDICADORES TÉCNICOS
# ============================
def ema(series, period):
    return series.ewm(span=period, adjust=False).mean()

def rsi(series, period=14):
    delta = series.diff()
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)

    avg_gain = pd.Series(gain).rolling(period).mean()
    avg_loss = pd.Series(loss).rolling(period).mean()

    rs = avg_gain / avg_loss
    return 100 - (100 / (1 + rs))

def detectar_suporte_resistencia(df, janela=20):
    suportes, resistencias = [], []

    for i in range(janela, len(df)):
        min_local = df['low'].iloc[i-janela:i].min()
        max_local = df['high'].iloc[i-janela:i].max()

        if min_local not in suportes:
            suportes.append(min_local)
        if max_local not in resistencias:
            resistencias.append(max_local)

    return suportes, resistencias

# ============================
# DETECÇÃO DE PADRÕES DE VELAS
# ============================
def detectar_padroes(vela_anterior, vela_atual):
    padroes = []

    corpo = abs(vela_atual['close'] - vela_atual['open'])
    pavio_sup = vela_atual['high'] - max(vela_atual['close'], vela_atual['open'])
    pavio_inf = min(vela_atual['close'], vela_atual['open']) - vela_atual['low']

    if vela_atual['close'] > vela_atual['open'] and vela_anterior['close'] < vela_anterior['open']:
        if vela_atual['close'] > vela_anterior['open'] and vela_atual['open'] < vela_anterior['close']:
            padroes.append("Engolfo de Alta")

    if vela_atual['close'] < vela_atual['open'] and vela_anterior['close'] > vela_anterior['open']:
        if vela_atual['close'] < vela_anterior['open'] and vela_atual['open'] > vela_anterior['close']:
            padroes.append("Engolfo de Baixa")

    if corpo <= ((vela_atual['high'] - vela_atual['low']) * 0.1):
        padroes.append("Doji")

    if corpo < (pavio_inf * 0.5) and pavio_inf > (corpo * 2):
        padroes.append("Martelo (possível reversão de alta)")

    if corpo < (pavio_sup * 0.5) and pavio_sup > (corpo * 2):
        padroes.append("Shooting Star (possível reversão de baixa)")

    return padroes

# ============================
# GERAÇÃO DE SINAIS
# ============================
def gerar_sinais(df, hora_ultima_vela):
    sinais = []

    df['EMA9'] = ema(df['close'], 9)
    df['EMA21'] = ema(df['close'], 21)
    df['EMA50'] = ema(df['close'], 50)
    df['RSI'] = rsi(df['close'])

    suportes, resistencias = detectar_suporte_resistencia(df)

    for i in range(1, len(df)):
        vela_ant = df.iloc[i-1]
        vela_atual = df.iloc[i]
        confirmacoes = []

        if vela_atual['EMA9'] > vela_atual['EMA21'] and vela_atual['EMA21'] > vela_atual['EMA50']:
            confirmacoes.append("Forte Tendência de Alta (EMAs alinhadas)")
        elif vela_atual['EMA9'] < vela_atual['EMA21'] and vela_atual['EMA21'] < vela_atual['EMA50']:
            confirmacoes.append("Forte Tendência de Baixa (EMAs alinhadas)")

        if vela_atual['RSI'] < 30:
            confirmacoes.append("RSI Sobrevendido")
        elif vela_atual['RSI'] > 70:
            confirmacoes.append("RSI Sobrecomprado")
        elif 45 < vela_atual['RSI'] < 55:
            confirmacoes.append("RSI Neutro (mercado lateral)")

        padroes = detectar_padroes(vela_ant, vela_atual)
        if padroes:
            confirmacoes.extend(padroes)

        preco = vela_atual['close']
        proximidade_suporte = min([abs(preco - s) for s in suportes]) if suportes else 9999
        proximidade_resistencia = min([abs(preco - r) for r in resistencias]) if resistencias else 9999

        if proximidade_suporte <= 2:
            confirmacoes.append("Próximo de Suporte (possível compra)")
        if proximidade_resistencia <= 2:
            confirmacoes.append("Próximo de Resistência (possível venda)")

        if len(confirmacoes) >= 3:
            direcao = "CALL" if any("Alta" in c or "compra" in c for c in confirmacoes) else "PUT"
            hora_entrada = hora_ultima_vela + timedelta(minutes=i*6)

            sinais.append({
                "hora": hora_entrada.strftime("%H:%M"),
                "direcao": direcao,
                "confirmacoes": confirmacoes
            })

    return sinais

# ============================
# OBTENDO DADOS EM TEMPO REAL COM SELENIUM + OCR (com fallback e logs)
# ============================
def obter_dados_realtime():
    url = "https://tradingpoin.com/chart?pair=CRYIDX.B&source=Binomo&val=Z-CRY%2FIDX&timeframe=60&desc&description=CRYPTO%20/%20IDX"

    options = Options()
    options.add_argument("--headless")
    options.add_argument("--disable-gpu")
    driver = webdriver.Chrome(options=options)
    driver.get(url)
    time.sleep(5)

    dados = {"open": [], "close": [], "high": [], "low": []}

    try:
        print("[INFO] Tentando capturar dados via HTML...")
        candles = driver.find_elements(By.CLASS_NAME, "candle")
        for c in candles:
            try:
                o = float(c.get_attribute("data-open"))
                h = float(c.get_attribute("data-high"))
                l = float(c.get_attribute("data-low"))
                cl = float(c.get_attribute("data-close"))
                dados['open'].append(o)
                dados['high'].append(h)
                dados['low'].append(l)
                dados['close'].append(cl)
            except:
                continue
        if len(dados['open']) == 0:
            raise Exception("Sem dados no HTML")
        print(f"[SUCESSO] Capturado {len(dados['open'])} candles via HTML.")

    except Exception as e:
        print(f"[FALHA] Captura via HTML falhou: {e}")
        print("[INFO] Tentando fallback para OCR...")
        screenshot = driver.get_screenshot_as_png()
        with open("chart.png", "wb") as f:
            f.write(screenshot)

        img = cv2.imread("chart.png")
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        text = pytesseract.image_to_string(gray)

        linhas = text.split("\n")
        for linha in linhas:
            if "O:" in linha and "H:" in linha and "L:" in linha and "C:" in linha:
                try:
                    o = float(linha.split("O:")[1].split()[0])
                    h = float(linha.split("H:")[1].split()[0])
                    l = float(linha.split("L:")[1].split()[0])
                    c = float(linha.split("C:")[1].split()[0])
                    dados['open'].append(o)
                    dados['high'].append(h)
                    dados['low'].append(l)
                    dados['close'].append(c)
                except:
                    continue
        print(f"[SUCESSO] Capturado {len(dados['open'])} candles via OCR.")

    driver.quit()
    return pd.DataFrame(dados)

# ============================
# EXEMPLO DE USO EM TEMPO REAL
# ============================
if __name__ == "__main__":
    df = obter_dados_realtime()  # tenta HTML, se falhar usa OCR
    hora_ultima_vela = datetime.strptime("12:34", "%H:%M")
    sinais = gerar_sinais(df, hora_ultima_vela)

    for s in sinais:
        print(s)
