import pandas as pd
import numpy as np
import time
import os
import json
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv
from binomo_data_provider import BinomoDataProvider

# Carregar variáveis de ambiente
load_dotenv()

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuração da API Binomo
BINOMO_CONFIG = {
    'BINOMO_API_BASE': 'https://api.binomo.com',
    'SYMBOL': 'Z-CRY%2FIDX',
    'TIMEFRAME': '1',
    'LOCALE': 'br',
    'REQUEST_TIMEOUT': 10,
    'MAX_RETRIES': 3,
    'CACHE_DURATION': 30  # Cache por 30 segundos para dados mais dinâmicos
}

# ============================
# INDICADORES TÉCNICOS
# ============================
def ema(series, period):
    return series.ewm(span=period, adjust=False).mean()

def rsi(series, period=14):
    delta = series.diff()
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)

    avg_gain = pd.Series(gain).rolling(period).mean()
    avg_loss = pd.Series(loss).rolling(period).mean()

    rs = avg_gain / avg_loss
    return 100 - (100 / (1 + rs))

def detectar_suporte_resistencia(df, janela=20):
    suportes, resistencias = [], []

    for i in range(janela, len(df)):
        min_local = df['low'].iloc[i-janela:i].min()
        max_local = df['high'].iloc[i-janela:i].max()

        if min_local not in suportes:
            suportes.append(min_local)
        if max_local not in resistencias:
            resistencias.append(max_local)

    return suportes, resistencias

# ============================
# DETECÇÃO DE PADRÕES DE VELAS
# ============================
def detectar_padroes(vela_anterior, vela_atual):
    padroes = []

    corpo = abs(vela_atual['close'] - vela_atual['open'])
    pavio_sup = vela_atual['high'] - max(vela_atual['close'], vela_atual['open'])
    pavio_inf = min(vela_atual['close'], vela_atual['open']) - vela_atual['low']

    if vela_atual['close'] > vela_atual['open'] and vela_anterior['close'] < vela_anterior['open']:
        if vela_atual['close'] > vela_anterior['open'] and vela_atual['open'] < vela_anterior['close']:
            padroes.append("Engolfo de Alta")

    if vela_atual['close'] < vela_atual['open'] and vela_anterior['close'] > vela_anterior['open']:
        if vela_atual['close'] < vela_anterior['open'] and vela_atual['open'] > vela_anterior['close']:
            padroes.append("Engolfo de Baixa")

    if corpo <= ((vela_atual['high'] - vela_atual['low']) * 0.1):
        padroes.append("Doji")

    if corpo < (pavio_inf * 0.5) and pavio_inf > (corpo * 2):
        padroes.append("Martelo (possível reversão de alta)")

    if corpo < (pavio_sup * 0.5) and pavio_sup > (corpo * 2):
        padroes.append("Shooting Star (possível reversão de baixa)")

    return padroes

# ============================
# GERAÇÃO DE SINAIS
# ============================
def gerar_sinais(df, hora_ultima_vela):
    sinais = []

    df['EMA9'] = ema(df['close'], 9)
    df['EMA21'] = ema(df['close'], 21)
    df['EMA50'] = ema(df['close'], 50)
    df['RSI'] = rsi(df['close'])

    suportes, resistencias = detectar_suporte_resistencia(df)

    for i in range(1, len(df)):
        vela_ant = df.iloc[i-1]
        vela_atual = df.iloc[i]
        confirmacoes = []

        if vela_atual['EMA9'] > vela_atual['EMA21'] and vela_atual['EMA21'] > vela_atual['EMA50']:
            confirmacoes.append("Forte Tendência de Alta (EMAs alinhadas)")
        elif vela_atual['EMA9'] < vela_atual['EMA21'] and vela_atual['EMA21'] < vela_atual['EMA50']:
            confirmacoes.append("Forte Tendência de Baixa (EMAs alinhadas)")

        if vela_atual['RSI'] < 30:
            confirmacoes.append("RSI Sobrevendido")
        elif vela_atual['RSI'] > 70:
            confirmacoes.append("RSI Sobrecomprado")
        elif 45 < vela_atual['RSI'] < 55:
            confirmacoes.append("RSI Neutro (mercado lateral)")

        padroes = detectar_padroes(vela_ant, vela_atual)
        if padroes:
            confirmacoes.extend(padroes)

        preco = vela_atual['close']
        proximidade_suporte = min([abs(preco - s) for s in suportes]) if suportes else 9999
        proximidade_resistencia = min([abs(preco - r) for r in resistencias]) if resistencias else 9999

        if proximidade_suporte <= 2:
            confirmacoes.append("Próximo de Suporte (possível compra)")
        if proximidade_resistencia <= 2:
            confirmacoes.append("Próximo de Resistência (possível venda)")

        if len(confirmacoes) >= 3:
            direcao = "CALL" if any("Alta" in c or "compra" in c for c in confirmacoes) else "PUT"
            hora_entrada = hora_ultima_vela + timedelta(minutes=i*6)

            sinais.append({
                "hora": hora_entrada.strftime("%H:%M"),
                "direcao": direcao,
                "confirmacoes": confirmacoes
            })

    return sinais

# ============================
# OBTENDO DADOS EM TEMPO REAL COM API BINOMO
# ============================
def obter_dados_realtime(count=100, timeframe="1"):
    """
    Obtém dados em tempo real da API Binomo

    Args:
        count: Número de candles para obter (padrão: 100)
        timeframe: Timeframe em minutos ("1", "5", "15")

    Returns:
        DataFrame com dados OHLCV ou None em caso de erro
    """
    try:
        logger.info(f"[INFO] Obtendo dados da API Binomo - {count} candles, timeframe {timeframe}m")

        # Inicializar o provedor de dados
        provider = BinomoDataProvider(BINOMO_CONFIG)

        # Testar conexão
        if not provider.test_connection():
            logger.error("[ERRO] Falha na conexão com a API Binomo")
            return None

        # Obter dados históricos
        df = provider.get_historical_data(timeframe=timeframe, count=count)

        if df is not None and len(df) > 0:
            logger.info(f"[SUCESSO] Obtidos {len(df)} candles da API Binomo")
            logger.info(f"[INFO] Período: {df['timestamp'].min()} até {df['timestamp'].max()}")
            logger.info(f"[INFO] Último preço: {df.iloc[-1]['close']:.5f}")

            # Cleanup
            provider.shutdown()
            return df
        else:
            logger.error("[ERRO] Nenhum dado retornado pela API")
            provider.shutdown()
            return None

    except Exception as e:
        logger.error(f"[ERRO] Erro ao obter dados da API: {e}")
        return None

def obter_preco_atual():
    """
    Obtém o preço atual do ativo

    Returns:
        float: Preço atual ou None em caso de erro
    """
    try:
        provider = BinomoDataProvider(BINOMO_CONFIG)
        preco = provider.get_current_price()
        provider.shutdown()
        return preco
    except Exception as e:
        logger.error(f"[ERRO] Erro ao obter preço atual: {e}")
        return None

def obter_dados_tempo_real_continuo():
    """
    Obtém dados em tempo real de forma contínua (últimas 2 velas)

    Returns:
        DataFrame com as últimas velas ou None em caso de erro
    """
    try:
        provider = BinomoDataProvider(BINOMO_CONFIG)
        df = provider.get_real_time_data()
        provider.shutdown()
        return df
    except Exception as e:
        logger.error(f"[ERRO] Erro ao obter dados em tempo real: {e}")
        return None

# ============================
# EXEMPLO DE USO EM TEMPO REAL COM API BINOMO
# ============================
def executar_bot_tempo_real(intervalo_atualizacao=60):
    """
    Executa o bot em tempo real com atualizações periódicas

    Args:
        intervalo_atualizacao: Intervalo em segundos entre atualizações
    """
    logger.info("=== INICIANDO BOT CRYPTO IDX - TEMPO REAL ===")
    logger.info(f"Intervalo de atualização: {intervalo_atualizacao} segundos")

    while True:
        try:
            logger.info("\n" + "="*50)
            logger.info(f"ANÁLISE - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info("="*50)

            # Obter dados em tempo real
            df = obter_dados_realtime(count=100, timeframe="1")

            if df is None or len(df) == 0:
                logger.error("Falha ao obter dados. Tentando novamente em 30 segundos...")
                time.sleep(30)
                continue

            # Usar a timestamp da última vela
            hora_ultima_vela = df.iloc[-1]['timestamp']

            # Gerar sinais
            sinais = gerar_sinais(df, hora_ultima_vela)

            # Exibir resultados
            if sinais:
                logger.info(f"\n🎯 SINAIS ENCONTRADOS ({len(sinais)}):")
                for i, sinal in enumerate(sinais, 1):
                    logger.info(f"\n--- SINAL {i} ---")
                    logger.info(f"⏰ Horário: {sinal['hora']}")
                    logger.info(f"📈 Direção: {sinal['direcao']}")
                    logger.info(f"✅ Confirmações ({len(sinal['confirmacoes'])}):")
                    for conf in sinal['confirmacoes']:
                        logger.info(f"   • {conf}")
            else:
                logger.info("❌ Nenhum sinal encontrado nesta análise")

            # Exibir informações do mercado
            preco_atual = df.iloc[-1]['close']
            variacao = ((df.iloc[-1]['close'] - df.iloc[-2]['close']) / df.iloc[-2]['close']) * 100
            logger.info(f"\n📊 INFORMAÇÕES DO MERCADO:")
            logger.info(f"💰 Preço atual: {preco_atual:.5f}")
            logger.info(f"📈 Variação última vela: {variacao:+.2f}%")
            logger.info(f"📅 Última atualização: {hora_ultima_vela.strftime('%H:%M:%S')}")

            # Aguardar próxima atualização
            logger.info(f"\n⏳ Próxima análise em {intervalo_atualizacao} segundos...")
            time.sleep(intervalo_atualizacao)

        except KeyboardInterrupt:
            logger.info("\n🛑 Bot interrompido pelo usuário")
            break
        except Exception as e:
            logger.error(f"❌ Erro durante execução: {e}")
            logger.info("Tentando novamente em 30 segundos...")
            time.sleep(30)

def executar_analise_unica():
    """
    Executa uma análise única (sem loop contínuo)
    """
    logger.info("=== ANÁLISE ÚNICA - CRYPTO IDX ===")

    # Obter dados
    df = obter_dados_realtime(count=100, timeframe="1")

    if df is None or len(df) == 0:
        logger.error("❌ Falha ao obter dados da API")
        return

    # Usar a timestamp da última vela
    hora_ultima_vela = df.iloc[-1]['timestamp']

    # Gerar sinais
    sinais = gerar_sinais(df, hora_ultima_vela)

    # Exibir resultados
    print("\n" + "="*60)
    print("📊 RESULTADO DA ANÁLISE")
    print("="*60)

    if sinais:
        print(f"\n🎯 SINAIS ENCONTRADOS: {len(sinais)}")
        for i, sinal in enumerate(sinais, 1):
            print(f"\n--- SINAL {i} ---")
            print(f"⏰ Horário: {sinal['hora']}")
            print(f"📈 Direção: {sinal['direcao']}")
            print(f"✅ Confirmações ({len(sinal['confirmacoes'])}):")
            for conf in sinal['confirmacoes']:
                print(f"   • {conf}")
    else:
        print("\n❌ Nenhum sinal encontrado")

    # Informações do mercado
    preco_atual = df.iloc[-1]['close']
    variacao = ((df.iloc[-1]['close'] - df.iloc[-2]['close']) / df.iloc[-2]['close']) * 100
    print(f"\n📊 INFORMAÇÕES DO MERCADO:")
    print(f"💰 Preço atual: {preco_atual:.5f}")
    print(f"📈 Variação última vela: {variacao:+.2f}%")
    print(f"📅 Dados de: {df.iloc[0]['timestamp'].strftime('%H:%M')} até {df.iloc[-1]['timestamp'].strftime('%H:%M')}")
    print(f"📈 Total de velas analisadas: {len(df)}")

if __name__ == "__main__":
    import sys

    print("🚀 BOT CRYPTO IDX - BINOMO API")
    print("="*40)
    print("1. Análise única")
    print("2. Execução contínua (tempo real)")
    print("3. Teste de conexão")

    try:
        opcao = input("\nEscolha uma opção (1-3): ").strip()

        if opcao == "1":
            executar_analise_unica()
        elif opcao == "2":
            intervalo = input("Intervalo de atualização em segundos (padrão: 60): ").strip()
            intervalo = int(intervalo) if intervalo.isdigit() else 60
            executar_bot_tempo_real(intervalo)
        elif opcao == "3":
            logger.info("🔧 Testando conexão com API Binomo...")
            provider = BinomoDataProvider(BINOMO_CONFIG)
            if provider.test_connection():
                logger.info("✅ Conexão bem-sucedida!")
                preco = provider.get_current_price()
                if preco:
                    logger.info(f"💰 Preço atual: {preco:.5f}")
            else:
                logger.error("❌ Falha na conexão")
            provider.shutdown()
        else:
            print("❌ Opção inválida")

    except KeyboardInterrupt:
        print("\n🛑 Programa interrompido")
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
